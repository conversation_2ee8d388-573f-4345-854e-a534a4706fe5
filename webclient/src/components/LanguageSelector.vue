<template>
  <q-select
    outlined
    dense
    emit-value
    map-options
    options-dense
    :modelValue="props.modelValue"
    @update:modelValue="onUpdateValue"
    :options="filteredLanguages"
    :label="$t('dbMapProviderToken.language')"
    :rules="[validateBCP47]"
    class="pb-4"
    use-input
    hide-selected
    fill-input
    input-debounce="200"
    @filter="filterLanguage"
    v-clear-fix="props.clearFixParams"
    :error="!!inputError"
    :error-message="inputError"
    @blur="validateInput"
  >
    <!-- <template v-slot:selected-item="scope">
      <div v-if="isCustomLanguageCode(scope.opt)">
        {{ $t('dbMapProviderToken.custom') + ': ' + scope.opt }}
      </div>
      <div v-else>
        {{ getLanguageLabel(scope.opt) }}
      </div>
    </template> -->
  </q-select>
</template>

<script setup lang="ts">
import { check } from 'language-tags';
import { computed, onMounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';

const { t, } = useI18n()

const props = defineProps<{
  modelValue: string
  clearFixParams?: any[]
}>()

const emit = defineEmits<{
  // eslint-disable-next-line no-unused-vars
  (e: 'update:modelValue', value: string): void
}>()

// 存储输入错误信息
const inputError = ref<string>('')

// 处理值更新
const onUpdateValue = (val: string) => {
  if (val) {
    validateInput(val)
  } else {
    inputError.value = '' // 清空时重置错误状态
  }
  emit('update:modelValue', val)
}

// 验证输入值
const validateInput = (val: string | Event) => {
  const value = typeof val === 'string' ? val : props.modelValue
  if (value) {
    const validationResult = validateBCP47(value)
    if (validationResult !== true) {
      inputError.value = validationResult as string
    } else {
      inputError.value = ''
    }
  } else {
    inputError.value = ''
  }
}

// 常用语言选项
const commonLanguages = computed(() => [
  { label: `${t('language.zhCN')} (zh-CN)`, value: 'zh-CN' },
  { label: `${t('language.zhTW')} (zh-TW)`, value: 'zh-TW' },
  { label: `${t('language.enUS')} (en-US)`, value: 'en-US' },
  { label: `${t('language.enGB')} (en-GB)`, value: 'en-GB' },
  { label: `${t('language.ja')} (ja)`, value: 'ja' },
  { label: `${t('language.ko')} (ko)`, value: 'ko' },
  { label: `${t('language.fr')} (fr)`, value: 'fr' },
  { label: `${t('language.de')} (de)`, value: 'de' },
  { label: `${t('language.es')} (es)`, value: 'es' },
  { label: `${t('language.it')} (it)`, value: 'it' },
  { label: `${t('language.ru')} (ru)`, value: 'ru' },
  { label: `${t('language.pt')} (pt)`, value: 'pt' },
  { label: `${t('language.ar')} (ar)`, value: 'ar' }
])

// 过滤语言选项
const filteredLanguages = ref<{label: string, value: string}[]>([])

// 初始化过滤选项
onMounted(() => {
  filteredLanguages.value = commonLanguages.value
})

// 过滤语言选项的方法
const filterLanguage = (val: string, update: (callback: () => void) => void) => {
  if (val === '') {
    update(() => {
      filteredLanguages.value = commonLanguages.value
    })
    return
  }

  update(() => {
    const needle = val.toLowerCase()
    filteredLanguages.value = commonLanguages.value.filter(
      v => v.label.toLowerCase().indexOf(needle) > -1 ||
           v.value.toLowerCase().indexOf(needle) > -1
    )
  })
}

// 检查是否是自定义语言代码
const isCustomLanguageCode = (code: string): boolean => {
  return !commonLanguages.value.some(lang => lang.value === code)
}

// 获取语言显示标签
const getLanguageLabel = (code: string): string => {
  const found = commonLanguages.value.find(lang => lang.value === code)
  if (found) {
    return found.label
  }
  return `${t('dbMapProviderToken.custom')}: ${code}`
}

// 验证BCP 47语言标签格式
const validateBCP47 = (val: string | null | undefined) => {
  if (!val) return t('dbMapProviderToken.invalidLanguageFormat')

  try {
    // 使用language-tags库验证语言标签
    return check(val) || t('dbMapProviderToken.invalidLanguageFormat')
  } catch {
    return t('dbMapProviderToken.invalidLanguageFormat')
  }
}
</script>