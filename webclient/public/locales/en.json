{"defaultOrgName": "Default Organization Name", "defaultProjectName": "Default Project Name", "copyrightInfo": "© BelFone (BFDX) All Rights Reserved", "common": {"back": "Return", "cancel": "Cancel", "confirm": "Confirm", "operationSuccess": "Operation successful", "operationFailed": "Operation failed", "operationProgress": "Processing... {progress}%", "operationPartialSuccess": "Operation partially successful: {success} succeeded, {fail} failed", "sessionExpired": "Login session expired", "sessionExpiredDesc": "Login has expired due to long period of inactivity. Please log in again.", "backToLogin": "Back to login page", "yes": "Yes", "no": "No", "permissionDenied": "No permission"}, "errorPage": {"notFoundDesc": "Sorry, the page you visited does not exist...", "backHome": "Back to home"}, "form": {"back": "@:common.back", "cancel": "@:common.cancel", "confirm": "@:common.confirm", "confirmSubmit": "Confirm Submit", "submit": "Submit", "add": "Add", "edit": "Edit", "delete": "Delete", "deleteConfirm": "Confirm Delete", "batchDelete": "Batch Deletion", "addSuccess": "Added successfully", "search": "Search", "deleteSuccess": "Delete successful", "updateSuccess": "Update successful", "keepAdd": "Keep adding"}, "password": "Password", "setup": {"confirmSubmitMsg": "This operation cannot be undone. Please confirm that the data is correct and click Confirm Submit!", "jumpNow": "Jump Now", "setFailed": "Setting failed", "setSuccess": "Setting Success", "setupFailedMessage": "Setting failed. Please check if the username and password are correct", "willJumpToHome": "After {second} seconds, you will automatically jump to the home page"}, "siteTitle": "Beifeng Map API Management Platform", "userName": "Username", "validate": {"allowedSpecifyChars": "Only letters, numbers and specified special characters ({chars}) can be entered", "passwordAtLeast": "The password must be at least {length} characters long", "required": "This field is required", "phoneValidFail": "The phone number format is incorrect", "emailValidFail": "Email format is incorrect", "nameUnique": "Name already exists, cannot be repeated", "numberRange": "Input range: {min} - {max}", "sysNameUnique": "System name already exists"}, "welcome": "Welcome to @:siteTitle", "welcomeInfo": "Please set the default administrator account information for the first use", "pages": {"dashboard": "API Usage", "dbOrg": "Organization", "baseUrl": {"google": "Enter the Google Maps API base URL", "tianditu": "Enter the Tianditu API base URL", "osm": "Enter the OpenStreetMap API base URL", "googleLocal": "Enter the local directory path for Google Maps tiles", "tiandituLocal": "Enter the local directory path for Tianditu tiles", "osmLocal": "Enter the local directory path for OpenStreetMap tiles"}, "dbProject": "Projects and API Key", "dbProjectToken": "API Key", "dbMapProviderToken": "Map source API key", "mapGl": "Map display"}, "myLogin": {"userName": "@:userName", "password": "@:password", "rememberAccount": "Remember account/password", "signIn": "Sign in", "loginSuccessful": "Login successful", "loginFailed": "Login failed!", "ReqTimeTooOld": "@:myLogin.loginFailed The client time and server time differ by more than five minutes", "UserNotExist": "@:myLogin.loginFailed User does not exist", "PasswordNotMatch": "@:myLogin.loginFailed Wrong username or password", "SessionIdNotExist": "@:myLogin.loginFailed Login key does not exist", "SessionIdExpired": "@:myLogin.loginFailed Login key has expired", "SessionAlreadyLogin": "@:myLogin.loginFailed The login key has been used elsewhere. Please log in again using your password.", "FailWithInternalError": "@:myLogin.loginFailed Server error, please try again later", "UserDisabled": "@:myLogin.loginFailed Your account has been disabled. Please contact the administrator."}, "language": {"zh": "中文", "en": "English", "enUS": "US English", "enGB": "UK English", "zhCN": "Simplified Chinese", "zhTW": "Traditional Chinese", "ja": "Japanese", "ko": "Korean", "fr": "French", "de": "German", "es": "Spanish", "it": "Italian", "ru": "Russian", "pt": "Portuguese", "ar": "Arabic"}, "myLayout": {"logOut": "Log out", "newAction": "New...", "newProject": "New Project", "newUser": "New User", "newApiKey": "Create a new API key", "newMapApiKey": "New map source API key"}, "dbUser": {"nickname": "Nickname", "email": "Email", "phone": "Phone", "note": "Note", "userManager": "User Management", "deleteConfirm": "Delete the current data and it will be irrecoverable. Please confirm whether to continue?", "canEditUser": "Can edit user", "operate": "operation", "createTime": "Creation time", "selectTooltip": "Press shift to select a range", "noData": "No data yet", "wantToDeleteUser": "Do you want to delete this user?", "userPrivilegeSetting": "User Privilege Setting", "userStatus": "User Status", "disableSuccess": "User disabled successfully", "enableSuccess": "User enabled successfully", "isDisableUser": "Whether to disable the user", "wantToDisableUser": "Do you want to disable this user?", "wantToEnableUser": "Do you want to enable this user?"}, "dbProject": {"quotasType": "Quotas type", "quotasLimit": "Quotas quantity", "usedQuotas": "Current usage", "quotasCalculateBy": "Calculate by {type}", "minute": "Minute", "hour": "Hour", "day": "Day", "month": "Month", "unlimited": "Unlimited", "wantToDisableProject": "Do you want to disable project?", "wantToEnableProject": "Do you want to enable project?", "wantToDeleteProject": "Do you want to delete project?", "deleteProject": "Delete project", "name": "Project Name", "project": "Project", "projectUpdateFail": "Project setup failed", "quotaUpdateFail": "Quota setup failed", "creator": "creator", "apiCount": "Number of api keys"}, "dbProjectToken": {"googleMap": "Google Map", "tianditu": "Tianditu", "openStreetMap": "OpenStreetMap", "localDirectory": "Local Directory", "availableMaps": "Which map platforms are available?", "expiryDate": "Expiry date", "alwaysValid": "Always valid", "wantToDisabledToken": "Disable API key?", "wantToEnableToken": "Enable API key?", "wantToDeleteToken": "Delete API key?", "disable": "Disabled", "enable": "Enabled", "apiKey": "API key", "status": "Status", "defaultRoadmap": "De<PERSON>ult Street Map", "defaultSatellite": "Default imagery (basemap only)", "defaultHybrid": "Default image (with annotations)", "sysNameHint": "Press Enter to add", "maxSysName": "Add up to 32 system names"}, "dbMapProviderToken": {"apiKey": "API key", "mapPlatform": "Map Platform", "apiKeyByMapPlatform": "API key provided by the map platform", "quotaCalcMethod": "Quota calculation method", "monthly": "Monthly", "daily": "Daily", "quotasUnlimited": "0 means unlimited", "googleMapType": "Map API Type", "language": "Language", "invalidLanguageFormat": "Invalid language format, please use BCP 47 format (e.g., en-US, zh-CN)", "invalidLocalDirectoryPath": "Please enter a valid local directory path", "custom": "Custom", "baseUrl": {"default": "De<PERSON>ult request address", "google": "Enter the Google Maps API base URL", "tianditu": "Enter the Tianditu API base URL", "osm": "Enter the OpenStreetMap API base URL", "googleLocal": "Enter the local directory path for Google Maps tiles", "tiandituLocal": "Enter the local directory path for Tianditu tiles", "osmLocal": "Enter the local directory path for OpenStreetMap tiles"}, "mapApiStatic": "Maps Static API", "mapApiTile": "Map Tiles API", "mapApiAll": "All", "apiName": "API key name", "copyToClipboardSuccess": "Copied to clipboard successfully", "clickToCopySysName": "Click to copy system name", "clickToCopyApiKey": "Click to copy API key", "sysName": "Application system name", "clickToLookSysName": "Click to see applicable system names", "priority": "Priority", "priorityDesc": "The higher the priority, the higher the priority", "isUseProxy": "Whether to use proxy"}, "dbOrg": {"name": "Unit name", "owner": "Owner", "wantToDeleteOrg": "Do you want to delete this unit?", "org": "Unit"}, "dashboard": {"projectCurrentUsage": "Current usage statistics of the project", "tokenCurrentUsage": "Current usage statistics of the [{name}] project API key", "mapApiQuotasChartTitle": "Current usage statistics of map source API keys", "mapApiQuotasChartAllUsageTitle": "Map source API key total usage statistics", "apiKey": "API key", "usage": "Usage", "projectStatistics": "Project Statistics", "currentUsage": "Current usage", "totalUsage": "Total usage", "alreadyUsedAPICount": "Number of used API keys", "clickToDetail": "Click to view the current usage statistics of the API key", "refresh": "Refresh", "back": "Back", "availableMapPlatforms": "Available map platforms", "defaultMapPlatforms": "Default map platform", "projectAllUsage": "Total usage statistics of the project", "projectTokenAllUsage": "Total usage statistics of [{name}] project API key", "usageNoDetail": "No usage for the current project", "streetMap": "street map", "satelliteMap": "image map", "googleMap": "Google Map", "tiandituMap": "tianditu", "refreshSuccess": "Refresh successful", "refreshFailed": "Refresh failed"}, "mapGL": {"lon": "longitude", "lat": "latitude", "maxLon": "Maximum longitude", "minLon": "Minimum longitude", "maxLat": "Maximum latitude", "minLat": "Minimum latitude", "boxSelectionInMap": "Box selection in map", "mapLevel": "Map level", "mapProvider": "Map provider", "mapType": "Map type", "cacheDateHint": "If left blank, caches for all time periods will be cleared by default", "clearCache": "Clear map cache", "onlyBaseMap": "Basemap only", "withAnnotation": "With annotation", "osmOnlySupportRoadMap": "OSM only supports street maps", "noCache": "Not cached yet", "clearCacheTime": "Clear caches for the specified time period", "selectionOperationInfo": "Drag a box to select an area on the map. After clicking the OK button, a clear map cache window will pop up to continue the operation. Press the ESC key to cancel the current operation.", "clearCacheSuccess": "Clear cache successfully", "refreshMap": "Refresh map", "clickBtnToOften": "Do not click the refresh button too often", "checkGcj02": "wgs84 and gcj02 conversion", "currentMap": "Current map", "currentCoordinateType": "Current coordinate type", "setLocate": "Set the default center point", "jumpDefaultLocate": "Jump to the default center point", "setDefaultMapCenterInfo": "Drag the map to select the default center point of the map. After clicking the OK button, a window for setting the default center point will pop up to continue the operation. Press the ESC key to cancel the current operation."}}